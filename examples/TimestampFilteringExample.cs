using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using TodoAPI.Models;

namespace TodoAPI.Examples
{
    /// <summary>
    /// Example demonstrating how to use the timestamp filtering functionality
    /// </summary>
    public class TimestampFilteringExample
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public TimestampFilteringExample(string baseUrl = "https://localhost:7000/api")
        {
            _httpClient = new HttpClient();
            _baseUrl = baseUrl;
        }

        /// <summary>
        /// Demonstrates various timestamp filtering scenarios
        /// </summary>
        public async Task RunExamplesAsync()
        {
            Console.WriteLine("Todo API Timestamp Filtering Examples");
            Console.WriteLine("=====================================\n");

            try
            {
                // Example 1: Get incomplete todos from the last 7 days
                await GetIncompleteTodosLastWeek();

                // Example 2: Get incomplete todos from the last 24 hours
                await GetIncompleteTodosLastDay();

                // Example 3: Get incomplete todos from a specific date range
                await GetIncompleteTodosDateRange();

                // Example 4: Get all todos to see timestamp fields
                await GetAllTodosWithTimestamps();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        private async Task GetIncompleteTodosLastWeek()
        {
            Console.WriteLine("1. Getting incomplete todos from the last 7 days...");
            
            var fromDate = DateTime.UtcNow.AddDays(-7);
            var url = $"{_baseUrl}/todos/incomplete?fromDate={fromDate:yyyy-MM-ddTHH:mm:ssZ}";
            
            var todos = await _httpClient.GetFromJsonAsync<List<Todo>>(url);
            
            Console.WriteLine($"Found {todos?.Count ?? 0} incomplete todos from the last week:");
            PrintTodos(todos);
            Console.WriteLine();
        }

        private async Task GetIncompleteTodosLastDay()
        {
            Console.WriteLine("2. Getting incomplete todos from the last 24 hours...");
            
            var fromDate = DateTime.UtcNow.AddDays(-1);
            var url = $"{_baseUrl}/todos/incomplete?fromDate={fromDate:yyyy-MM-ddTHH:mm:ssZ}";
            
            var todos = await _httpClient.GetFromJsonAsync<List<Todo>>(url);
            
            Console.WriteLine($"Found {todos?.Count ?? 0} incomplete todos from the last day:");
            PrintTodos(todos);
            Console.WriteLine();
        }

        private async Task GetIncompleteTodosDateRange()
        {
            Console.WriteLine("3. Getting incomplete todos from a specific date range...");
            
            var fromDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var toDate = new DateTime(2024, 1, 31, 23, 59, 59, DateTimeKind.Utc);
            
            var url = $"{_baseUrl}/todos/incomplete?fromDate={fromDate:yyyy-MM-ddTHH:mm:ssZ}&toDate={toDate:yyyy-MM-ddTHH:mm:ssZ}";
            
            var todos = await _httpClient.GetFromJsonAsync<List<Todo>>(url);
            
            Console.WriteLine($"Found {todos?.Count ?? 0} incomplete todos from January 2024:");
            PrintTodos(todos);
            Console.WriteLine();
        }

        private async Task GetAllTodosWithTimestamps()
        {
            Console.WriteLine("4. Getting all todos to show timestamp fields...");
            
            var url = $"{_baseUrl}/todos";
            var todos = await _httpClient.GetFromJsonAsync<List<Todo>>(url);
            
            Console.WriteLine($"All todos ({todos?.Count ?? 0} total):");
            PrintTodos(todos, showTimestamps: true);
            Console.WriteLine();
        }

        private void PrintTodos(List<Todo>? todos, bool showTimestamps = false)
        {
            if (todos == null || todos.Count == 0)
            {
                Console.WriteLine("  No todos found.");
                return;
            }

            foreach (var todo in todos)
            {
                var status = todo.Completed ? "✓" : "○";
                Console.WriteLine($"  {status} {todo.Name} (ID: {todo.Id})");
                
                if (showTimestamps)
                {
                    Console.WriteLine($"    Created: {todo.CreatedAt:yyyy-MM-dd HH:mm:ss} UTC");
                    Console.WriteLine($"    Updated: {todo.UpdatedAt:yyyy-MM-dd HH:mm:ss} UTC");
                }
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// Program entry point for running the examples
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var example = new TimestampFilteringExample();
            
            try
            {
                await example.RunExamplesAsync();
            }
            finally
            {
                example.Dispose();
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
