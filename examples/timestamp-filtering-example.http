# Todo API Timestamp Filtering Examples
# Use these examples with REST clients like VS Code REST Client extension

### Variables
@baseUrl = https://localhost:7000/api
@authToken = your-jwt-token-here

### 1. Create a few sample todos (requires authentication)
POST {{baseUrl}}/todos
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "name": "Complete project documentation",
  "completed": false
}

###
POST {{baseUrl}}/todos
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "name": "Review pull requests",
  "completed": false
}

###
POST {{baseUrl}}/todos
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "name": "Update dependencies",
  "completed": true
}

### 2. Get all todos (to see the new timestamp fields)
GET {{baseUrl}}/todos

### 3. Get incomplete todos created in the last 7 days
GET {{baseUrl}}/todos/incomplete?fromDate={{$datetime iso8601 -7 d}}

### 4. Get incomplete todos created in the last 24 hours
GET {{baseUrl}}/todos/incomplete?fromDate={{$datetime iso8601 -1 d}}

### 5. Get incomplete todos created between specific dates
GET {{baseUrl}}/todos/incomplete?fromDate=2024-01-20T00:00:00Z&toDate=2024-01-27T23:59:59Z

### 6. Get incomplete todos created from a specific date onwards
GET {{baseUrl}}/todos/incomplete?fromDate=2024-01-01T00:00:00Z

### 7. Update a todo (to see UpdatedAt timestamp change)
PUT {{baseUrl}}/todos/507f1f77bcf86cd799439011
Content-Type: application/json

{
  "id": "507f1f77bcf86cd799439011",
  "name": "Complete project documentation - UPDATED",
  "completed": false
}

### 8. Get the updated todo to verify timestamp changes
GET {{baseUrl}}/todos/507f1f77bcf86cd799439011
