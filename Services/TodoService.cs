using MongoDB.Bson;
using MongoDB.Driver;
using TodoAPI.Models;

namespace TodoAPI.Services
{
    public class TodoService(IMongoDatabase database)
    {
        private readonly IMongoCollection<Todo> _todos = database.GetCollection<Todo>("Todos");

        public virtual async Task<List<Todo>> GetTodosAsync()
        {
            return await _todos.Find(todo => true).ToListAsync();
        }

        public virtual async Task<Todo?> GetTodoAsync(string id)
        {
            return await _todos.Find(todo => todo.Id == id).FirstOrDefaultAsync();
        }

        public virtual async Task<Todo> CreateTodoAsync(Todo todo)
        {
            todo.CreatedAt = DateTime.UtcNow;
            todo.UpdatedAt = DateTime.UtcNow;
            await _todos.InsertOneAsync(todo);
            return todo;
        }

        public virtual async Task UpdateTodoAsync(string id, Todo newTodo)
        {
            newTodo.UpdatedAt = DateTime.UtcNow;
            await _todos.ReplaceOneAsync(todo => todo.Id == id, newTodo);
        }

        public virtual async Task DeleteTodoAsync(string id)
        {
            await _todos.DeleteOneAsync(todo => todo.Id == id);
        }

        public virtual async Task<List<Todo>> SearchTodosByNameAsync(string name)
        {
            var filter = Builders<Todo>.Filter.Regex("Name", new BsonRegularExpression(name, "i"));
            return await _todos.Find(filter).ToListAsync();
        }

        public virtual async Task<List<Todo>> GetIncompleteTodosByCreationDateAsync(DateTime fromDate, DateTime? toDate = null)
        {
            var filterBuilder = Builders<Todo>.Filter;
            var filters = new List<FilterDefinition<Todo>>
            {
                filterBuilder.Eq(todo => todo.Completed, false),
                filterBuilder.Gte(todo => todo.CreatedAt, fromDate)
            };

            if (toDate.HasValue)
            {
                filters.Add(filterBuilder.Lte(todo => todo.CreatedAt, toDate.Value));
            }

            var combinedFilter = filterBuilder.And(filters);
            return await _todos.Find(combinedFilter).SortBy(todo => todo.CreatedAt).ToListAsync();
        }

        public virtual async Task<List<Todo>> SearchTodosByTagAsync(string tag)
        {
            var filter = Builders<Todo>.Filter.AnyEq(todo => todo.Tags, tag);
            return await _todos.Find(filter).ToListAsync();
        }

        public virtual async Task<List<Todo>> SearchTodosByTagsAsync(List<string> tags)
        {
            var filter = Builders<Todo>.Filter.AnyIn(todo => todo.Tags, tags);
            return await _todos.Find(filter).ToListAsync();
        }
    }
}