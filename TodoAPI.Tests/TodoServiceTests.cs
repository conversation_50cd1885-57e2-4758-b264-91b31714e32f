using Xunit;
using Moq;
using MongoDB.Driver;
using TodoAPI.Models;
using TodoAPI.Services;
using MongoDB.Bson;

namespace TodoAPI.TodoAPI.Tests
{
    public class TodoServiceTests
    {
        private readonly Mock<IMongoCollection<Todo>> _mockCollection;
        private readonly Mock<IMongoDatabase> _mockDatabase;
        private readonly TodoService _service;

        public TodoServiceTests()
        {
            _mockCollection = new Mock<IMongoCollection<Todo>>();
            _mockDatabase = new Mock<IMongoDatabase>();
            
            _mockDatabase.Setup(db => db.GetCollection<Todo>("Todos", null))
                        .Returns(_mockCollection.Object);
            
            _service = new TodoService(_mockDatabase.Object);
        }

        [Fact]
        public async Task GetTodosAsync_ReturnsListOfTodos()
        {
            // Arrange
            var todos = new List<Todo> 
            { 
                new Todo { Id = "507f1f77bcf86cd799439011", Name = "Test Todo 1", Completed = false },
                new Todo { Id = "507f1f77bcf86cd799439012", Name = "Test Todo 2", Completed = true }
            };

            var mockCursor = new Mock<IAsyncCursor<Todo>>();
            mockCursor.Setup(_ => _.Current).Returns(todos);
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(true)
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(true)
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<Todo>>(),
                It.IsAny<FindOptions<Todo, Todo>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.GetTodosAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("Test Todo 1", result[0].Name);
            Assert.Equal("Test Todo 2", result[1].Name);
        }

        [Fact]
        public async Task GetTodoAsync_WithValidId_ReturnsTodo()
        {
            // Arrange
            var todoId = "507f1f77bcf86cd799439011";
            var todo = new Todo { Id = todoId, Name = "Test Todo", Completed = false };

            var mockCursor = new Mock<IAsyncCursor<Todo>>();
            mockCursor.Setup(_ => _.Current).Returns(new List<Todo> { todo });
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(true)
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(true)
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<Todo>>(),
                It.IsAny<FindOptions<Todo, Todo>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.GetTodoAsync(todoId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(todoId, result.Id);
            Assert.Equal("Test Todo", result.Name);
        }

        [Fact]
        public async Task GetTodoAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            var todoId = "507f1f77bcf86cd799439011";

            var mockCursor = new Mock<IAsyncCursor<Todo>>();
            mockCursor.Setup(_ => _.Current).Returns(new List<Todo>());
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<Todo>>(),
                It.IsAny<FindOptions<Todo, Todo>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.GetTodoAsync(todoId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task CreateTodoAsync_InsertsTodoAndReturnsIt()
        {
            // Arrange
            var todo = new Todo { Name = "New Todo", Completed = false };
            var beforeCreate = DateTime.UtcNow;

            _mockCollection.Setup(c => c.InsertOneAsync(
                It.IsAny<Todo>(),
                It.IsAny<InsertOneOptions>(),
                It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _service.CreateTodoAsync(todo);
            var afterCreate = DateTime.UtcNow;

            // Assert
            _mockCollection.Verify(c => c.InsertOneAsync(
                todo,
                It.IsAny<InsertOneOptions>(),
                It.IsAny<CancellationToken>()), Times.Once);
            Assert.Equal(todo, result);
            Assert.True(result.CreatedAt >= beforeCreate && result.CreatedAt <= afterCreate);
            Assert.True(result.UpdatedAt >= beforeCreate && result.UpdatedAt <= afterCreate);
        }

        [Fact]
        public async Task UpdateTodoAsync_ReplacesTodo()
        {
            // Arrange
            var todoId = "507f1f77bcf86cd799439011";
            var updatedTodo = new Todo { Id = todoId, Name = "Updated Todo", Completed = true };
            var beforeUpdate = DateTime.UtcNow;

            _mockCollection.Setup(c => c.ReplaceOneAsync(
                It.IsAny<FilterDefinition<Todo>>(),
                It.IsAny<Todo>(),
                It.IsAny<ReplaceOptions>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ReplaceOneResult.Acknowledged(1, 1, null));

            // Act
            await _service.UpdateTodoAsync(todoId, updatedTodo);
            var afterUpdate = DateTime.UtcNow;

            // Assert
            _mockCollection.Verify(c => c.ReplaceOneAsync(
                It.IsAny<FilterDefinition<Todo>>(),
                updatedTodo,
                It.IsAny<ReplaceOptions>(),
                It.IsAny<CancellationToken>()), Times.Once);
            Assert.True(updatedTodo.UpdatedAt >= beforeUpdate && updatedTodo.UpdatedAt <= afterUpdate);
        }

        [Fact]
        public async Task DeleteTodoAsync_DeletesTodo()
        {
            // Arrange
            var todoId = "507f1f77bcf86cd799439011";

            _mockCollection.Setup(c => c.DeleteOneAsync(
                It.IsAny<FilterDefinition<Todo>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new DeleteResult.Acknowledged(1));

            // Act
            await _service.DeleteTodoAsync(todoId);

            // Assert
            _mockCollection.Verify(c => c.DeleteOneAsync(
                It.IsAny<FilterDefinition<Todo>>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task SearchTodosByNameAsync_ReturnsMatchingTodos()
        {
            // Arrange
            var searchTerm = "test";
            var todos = new List<Todo> 
            {
                new Todo { Id = "507f1f77bcf86cd799439011", Name = "Test Task", Completed = false },
                new Todo { Id = "507f1f77bcf86cd799439012", Name = "Testing", Completed = true }
            };

            var mockCursor = new Mock<IAsyncCursor<Todo>>();
            mockCursor.Setup(_ => _.Current).Returns(todos);
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(true)
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(true)
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<Todo>>(),
                It.IsAny<FindOptions<Todo, Todo>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.SearchTodosByNameAsync(searchTerm);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.All(result, todo => Assert.Contains(searchTerm, todo.Name, StringComparison.OrdinalIgnoreCase));
        }

        [Fact]
        public async Task SearchTodosByNameAsync_WithNoMatches_ReturnsEmptyList()
        {
            // Arrange
            var searchTerm = "nonexistent";

            var mockCursor = new Mock<IAsyncCursor<Todo>>();
            mockCursor.Setup(_ => _.Current).Returns(new List<Todo>());
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<Todo>>(),
                It.IsAny<FindOptions<Todo, Todo>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.SearchTodosByNameAsync(searchTerm);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetIncompleteTodosByCreationDateAsync_ReturnsFilteredTodos()
        {
            // Arrange
            var fromDate = DateTime.UtcNow.AddDays(-7);
            var toDate = DateTime.UtcNow;
            var incompleteTodos = new List<Todo>
            {
                new Todo { Id = "1", Name = "Todo 1", Completed = false, CreatedAt = DateTime.UtcNow.AddDays(-5) },
                new Todo { Id = "2", Name = "Todo 2", Completed = false, CreatedAt = DateTime.UtcNow.AddDays(-3) }
            };

            var mockCursor = new Mock<IAsyncCursor<Todo>>();
            mockCursor.Setup(c => c.Current).Returns(incompleteTodos);
            mockCursor.SetupSequence(c => c.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(true)
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<Todo>>(),
                It.IsAny<FindOptions<Todo, Todo>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.GetIncompleteTodosByCreationDateAsync(fromDate, toDate);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.All(result, todo => Assert.False(todo.Completed));
            Assert.All(result, todo => Assert.True(todo.CreatedAt >= fromDate && todo.CreatedAt <= toDate));
        }

        [Fact]
        public async Task GetIncompleteTodosByCreationDateAsync_WithoutToDate_ReturnsFilteredTodos()
        {
            // Arrange
            var fromDate = DateTime.UtcNow.AddDays(-7);
            var incompleteTodos = new List<Todo>
            {
                new Todo { Id = "1", Name = "Todo 1", Completed = false, CreatedAt = DateTime.UtcNow.AddDays(-5) },
                new Todo { Id = "2", Name = "Todo 2", Completed = false, CreatedAt = DateTime.UtcNow.AddDays(-1) }
            };

            var mockCursor = new Mock<IAsyncCursor<Todo>>();
            mockCursor.Setup(c => c.Current).Returns(incompleteTodos);
            mockCursor.SetupSequence(c => c.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(true)
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<Todo>>(),
                It.IsAny<FindOptions<Todo, Todo>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.GetIncompleteTodosByCreationDateAsync(fromDate);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.All(result, todo => Assert.False(todo.Completed));
            Assert.All(result, todo => Assert.True(todo.CreatedAt >= fromDate));
        }
    }
}
