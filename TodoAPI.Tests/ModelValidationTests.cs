using Xunit;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using TodoAPI.Models;

namespace TodoAPI.Tests
{
    public class ModelValidationTests
    {
        private static IList<ValidationResult> ValidateModel(object model)
        {
            var validationResults = new List<ValidationResult>();
            var ctx = new ValidationContext(model, null, null);
            Validator.TryValidateObject(model, ctx, validationResults, true);
            return validationResults;
        }

        [Fact]
        public void User_WithValidData_PassesValidation()
        {
            // Arrange
            var user = new User
            {
                Username = "testuser",
                PasswordHash = "hashedpassword",
                Email = "<EMAIL>",
                CreatedAt = DateTime.UtcNow
            };

            // Act
            var validationResults = ValidateModel(user);

            // Assert
            Assert.Empty(validationResults);
        }

        [Fact]
        public void User_WithoutUsername_FailsValidation()
        {
            // Arrange
            var user = new User
            {
                Username = null!, // Required field is null
                PasswordHash = "hashedpassword",
                Email = "<EMAIL>",
                CreatedAt = DateTime.UtcNow
            };

            // Act
            var validationResults = ValidateModel(user);

            // Assert
            Assert.NotEmpty(validationResults);
            Assert.Contains(validationResults, vr => vr.MemberNames.Contains("Username"));
        }

        [Fact]
        public void User_WithoutPasswordHash_FailsValidation()
        {
            // Arrange
            var user = new User
            {
                Username = "testuser",
                PasswordHash = null!, // Required field is null
                Email = "<EMAIL>",
                CreatedAt = DateTime.UtcNow
            };

            // Act
            var validationResults = ValidateModel(user);

            // Assert
            Assert.NotEmpty(validationResults);
            Assert.Contains(validationResults, vr => vr.MemberNames.Contains("PasswordHash"));
        }

        [Fact]
        public void User_WithInvalidEmail_FailsValidation()
        {
            // Arrange
            var user = new User
            {
                Username = "testuser",
                PasswordHash = "hashedpassword",
                Email = "invalid-email", // Invalid email format
                CreatedAt = DateTime.UtcNow
            };

            // Act
            var validationResults = ValidateModel(user);

            // Assert
            Assert.NotEmpty(validationResults);
            Assert.Contains(validationResults, vr => vr.MemberNames.Contains("Email"));
        }

        [Fact]
        public void User_WithNullEmail_PassesValidation()
        {
            // Arrange
            var user = new User
            {
                Username = "testuser",
                PasswordHash = "hashedpassword",
                Email = null, // Email is optional
                CreatedAt = DateTime.UtcNow
            };

            // Act
            var validationResults = ValidateModel(user);

            // Assert
            Assert.Empty(validationResults);
        }

        [Fact]
        public void TodoItem_WithValidData_PassesValidation()
        {
            // Arrange
            var todoItem = new TodoItem
            {
                Id = "507f1f77bcf86cd799439011",
                Name = "Test Todo",
                Completed = false
            };

            // Act
            var validationResults = ValidateModel(todoItem);

            // Assert
            Assert.Empty(validationResults);
        }

        [Fact]
        public void TodoItem_WithoutName_FailsValidation()
        {
            // Arrange
            var todoItem = new TodoItem
            {
                Id = "507f1f77bcf86cd799439011",
                Name = null, // Required field is null
                Completed = false
            };

            // Act
            var validationResults = ValidateModel(todoItem);

            // Assert
            Assert.NotEmpty(validationResults);
            Assert.Contains(validationResults, vr => vr.MemberNames.Contains("Name"));
        }

        [Fact]
        public void TodoItem_WithEmptyName_FailsValidation()
        {
            // Arrange
            var todoItem = new TodoItem
            {
                Id = "507f1f77bcf86cd799439011",
                Name = "", // Empty string should fail MinLength validation
                Completed = false
            };

            // Act
            var validationResults = ValidateModel(todoItem);

            // Assert
            Assert.NotEmpty(validationResults);
            Assert.Contains(validationResults, vr => vr.MemberNames.Contains("Name"));
        }

        [Fact]
        public void TodoItem_WithWhitespaceOnlyName_FailsValidation()
        {
            // Arrange
            var todoItem = new TodoItem
            {
                Id = "507f1f77bcf86cd799439011",
                Name = "   ", // Whitespace only should fail MinLength validation
                Completed = false
            };

            // Act
            var validationResults = ValidateModel(todoItem);

            // Assert
            Assert.NotEmpty(validationResults);
            Assert.Contains(validationResults, vr => vr.MemberNames.Contains("Name"));
        }

        [Fact]
        public void Todo_WithValidData_HasNoValidationAttributes()
        {
            // Arrange
            var todo = new Todo
            {
                Id = "507f1f77bcf86cd799439011",
                Name = "Test Todo",
                Completed = false,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Act
            var validationResults = ValidateModel(todo);

            // Assert
            // Todo model doesn't have validation attributes, so it should always pass
            Assert.Empty(validationResults);
        }

        [Fact]
        public void Todo_WithNullName_PassesValidation()
        {
            // Arrange
            var todo = new Todo
            {
                Id = "507f1f77bcf86cd799439011",
                Name = null, // Todo model doesn't have validation attributes
                Completed = false,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Act
            var validationResults = ValidateModel(todo);

            // Assert
            // Todo model doesn't have validation attributes, so it should always pass
            Assert.Empty(validationResults);
        }

        [Theory]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        public void User_WithValidEmailFormats_PassesValidation(string email)
        {
            // Arrange
            var user = new User
            {
                Username = "testuser",
                PasswordHash = "hashedpassword",
                Email = email,
                CreatedAt = DateTime.UtcNow
            };

            // Act
            var validationResults = ValidateModel(user);

            // Assert
            Assert.Empty(validationResults);
        }

        [Theory]
        [InlineData("invalid-email")]
        [InlineData("@example.com")]
        [InlineData("test@")]
        [InlineData("invalid@<EMAIL>")]
        public void User_WithInvalidEmailFormats_FailsValidation(string email)
        {
            // Arrange
            var user = new User
            {
                Username = "testuser",
                PasswordHash = "hashedpassword",
                Email = email,
                CreatedAt = DateTime.UtcNow
            };

            // Act
            var validationResults = ValidateModel(user);

            // Assert
            Assert.NotEmpty(validationResults);
            Assert.Contains(validationResults, vr => vr.MemberNames.Contains("Email"));
        }

        [Fact]
        public void Todo_DefaultTimestamps_AreSetToCurrentTime()
        {
            // Arrange
            var beforeCreation = DateTime.UtcNow;

            // Act
            var todo = new Todo
            {
                Id = "507f1f77bcf86cd799439011",
                Name = "Test Todo",
                Completed = false
            };
            var afterCreation = DateTime.UtcNow;

            // Assert
            Assert.True(todo.CreatedAt >= beforeCreation && todo.CreatedAt <= afterCreation);
            Assert.True(todo.UpdatedAt >= beforeCreation && todo.UpdatedAt <= afterCreation);
        }
    }
}