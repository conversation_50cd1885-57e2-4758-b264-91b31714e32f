using Xunit;
using Moq;
using Microsoft.AspNetCore.Mvc;
using TodoAPI.Controllers;
using TodoAPI.Models;
using TodoAPI.Services;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Collections.Generic;
using MongoDB.Driver;

namespace TodoAPI.Tests
{
    public class TodosControllerTests
    {
        private readonly Mock<TodoService> _mockTodoService;
        private readonly TodosController _controller;

        public TodosControllerTests()
        {
            _mockTodoService = new Mock<TodoService>(Mock.Of<MongoDB.Driver.IMongoDatabase>());
            _controller = new TodosController(_mockTodoService.Object);
        }

        [Fact]
        public async Task GetTodos_ReturnsOkResultWithTodos()
        {
            // Arrange
            var todos = new List<Todo>
            {
                new Todo { Id = "507f1f77bcf86cd799439011", Name = "Todo 1", Completed = false },
                new Todo { Id = "507f1f77bcf86cd799439012", Name = "Todo 2", Completed = true }
            };

            _mockTodoService.Setup(s => s.GetTodosAsync())
                           .ReturnsAsync(todos);

            // Act
            var result = await _controller.GetTodos();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedTodos = Assert.IsType<List<Todo>>(okResult.Value);
            Assert.Equal(2, returnedTodos.Count);
            Assert.Equal("Todo 1", returnedTodos[0].Name);
        }

        [Fact]
        public async Task GetTodo_WithValidId_ReturnsOkResultWithTodo()
        {
            // Arrange
            var todoId = "507f1f77bcf86cd799439011";
            var todo = new Todo { Id = todoId, Name = "Test Todo", Completed = false };

            _mockTodoService.Setup(s => s.GetTodoAsync(todoId))
                           .ReturnsAsync(todo);

            // Act
            var result = await _controller.GetTodo(todoId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedTodo = Assert.IsType<Todo>(okResult.Value);
            Assert.Equal(todoId, returnedTodo.Id);
            Assert.Equal("Test Todo", returnedTodo.Name);
        }

        [Fact]
        public async Task GetTodo_WithInvalidId_ReturnsNotFound()
        {
            // Arrange
            var todoId = "507f1f77bcf86cd799439011";

            _mockTodoService.Setup(s => s.GetTodoAsync(todoId))
                           .ReturnsAsync((Todo?)null);

            // Act
            var result = await _controller.GetTodo(todoId);

            // Assert
            Assert.IsType<NotFoundResult>(result.Result);
        }

        [Fact]
        public async Task CreateTodo_WithValidTodo_ReturnsCreatedAtRoute()
        {
            // Arrange
            var todo = new Todo { Name = "New Todo", Completed = false };
            var createdTodo = new Todo { Id = "507f1f77bcf86cd799439011", Name = "New Todo", Completed = false };

            _mockTodoService.Setup(s => s.CreateTodoAsync(It.IsAny<Todo>()))
                           .ReturnsAsync(createdTodo);

            // Setup user context for authorization
            var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
            {
                new Claim(ClaimTypes.Name, "testuser")
            }, "mock"));

            _controller.ControllerContext = new ControllerContext()
            {
                HttpContext = new DefaultHttpContext() { User = user }
            };

            // Act
            var result = await _controller.CreateTodo(todo);

            // Assert
            var createdResult = Assert.IsType<CreatedAtRouteResult>(result.Result);
            Assert.Equal("GetTodo", createdResult.RouteName);
            Assert.Equal(createdTodo.Id, ((Todo)createdResult.Value!).Id);
            
            _mockTodoService.Verify(s => s.CreateTodoAsync(It.IsAny<Todo>()), Times.Once);
        }

        [Fact]
        public async Task CreateTodo_WithInvalidModel_ReturnsBadRequest()
        {
            // Arrange
            var todo = new Todo { Name = "", Completed = false }; // Invalid: empty name
            _controller.ModelState.AddModelError("Name", "Name is required");

            // Act
            var result = await _controller.CreateTodo(todo);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result.Result);
        }

        [Fact]
        public async Task UpdateTodo_WithValidTodo_ReturnsNoContent()
        {
            // Arrange
            var todoId = "507f1f77bcf86cd799439011";
            var existingTodo = new Todo { Id = todoId, Name = "Existing Todo", Completed = false };
            var updatedTodo = new Todo { Id = todoId, Name = "Updated Todo", Completed = true };

            _mockTodoService.Setup(s => s.GetTodoAsync(todoId))
                           .ReturnsAsync(existingTodo);
            _mockTodoService.Setup(s => s.UpdateTodoAsync(todoId, updatedTodo))
                           .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateTodo(todoId, updatedTodo);

            // Assert
            Assert.IsType<NoContentResult>(result);
            _mockTodoService.Verify(s => s.UpdateTodoAsync(todoId, updatedTodo), Times.Once);
        }

        [Fact]
        public async Task UpdateTodo_WithNonExistentTodo_ReturnsNotFound()
        {
            // Arrange
            var todoId = "507f1f77bcf86cd799439011";
            var updatedTodo = new Todo { Id = todoId, Name = "Updated Todo", Completed = true };

            _mockTodoService.Setup(s => s.GetTodoAsync(todoId))
                           .ReturnsAsync((Todo?)null);

            // Act
            var result = await _controller.UpdateTodo(todoId, updatedTodo);

            // Assert
            Assert.IsType<NotFoundResult>(result);
            _mockTodoService.Verify(s => s.UpdateTodoAsync(It.IsAny<string>(), It.IsAny<Todo>()), Times.Never);
        }

        [Fact]
        public async Task UpdateTodo_WithInvalidModel_ReturnsBadRequest()
        {
            // Arrange
            var todoId = "507f1f77bcf86cd799439011";
            var updatedTodo = new Todo { Id = todoId, Name = "", Completed = true }; // Invalid: empty name
            _controller.ModelState.AddModelError("Name", "Name is required");

            // Act
            var result = await _controller.UpdateTodo(todoId, updatedTodo);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task DeleteTodo_WithValidId_ReturnsNoContent()
        {
            // Arrange
            var todoId = "507f1f77bcf86cd799439011";
            var existingTodo = new Todo { Id = todoId, Name = "Todo to Delete", Completed = false };

            _mockTodoService.Setup(s => s.GetTodoAsync(todoId))
                           .ReturnsAsync(existingTodo);
            _mockTodoService.Setup(s => s.DeleteTodoAsync(todoId))
                           .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteTodo(todoId);

            // Assert
            Assert.IsType<NoContentResult>(result);
            _mockTodoService.Verify(s => s.DeleteTodoAsync(todoId), Times.Once);
        }

        [Fact]
        public async Task DeleteTodo_WithNonExistentTodo_ReturnsNotFound()
        {
            // Arrange
            var todoId = "507f1f77bcf86cd799439011";

            _mockTodoService.Setup(s => s.GetTodoAsync(todoId))
                           .ReturnsAsync((Todo?)null);

            // Act
            var result = await _controller.DeleteTodo(todoId);

            // Assert
            Assert.IsType<NotFoundResult>(result);
            _mockTodoService.Verify(s => s.DeleteTodoAsync(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task SearchTodosByName_ReturnsOkResultWithMatchingTodos()
        {
            // Arrange
            var searchTerm = "test";
            var matchingTodos = new List<Todo>
            {
                new Todo { Id = "507f1f77bcf86cd799439011", Name = "Test Todo 1", Completed = false },
                new Todo { Id = "507f1f77bcf86cd799439012", Name = "Testing Todo 2", Completed = true }
            };

            _mockTodoService.Setup(s => s.SearchTodosByNameAsync(searchTerm))
                           .ReturnsAsync(matchingTodos);

            // Act
            var result = await _controller.SearchTodosByName(searchTerm);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedTodos = Assert.IsType<List<Todo>>(okResult.Value);
            Assert.Equal(2, returnedTodos.Count);
            Assert.All(returnedTodos, todo => Assert.Contains(searchTerm, todo.Name, StringComparison.OrdinalIgnoreCase));
        }

        [Fact]
        public async Task SearchTodosByName_WithNoMatches_ReturnsEmptyList()
        {
            // Arrange
            var searchTerm = "nonexistent";
            var emptyList = new List<Todo>();

            _mockTodoService.Setup(s => s.SearchTodosByNameAsync(searchTerm))
                           .ReturnsAsync(emptyList);

            // Act
            var result = await _controller.SearchTodosByName(searchTerm);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedTodos = Assert.IsType<List<Todo>>(okResult.Value);
            Assert.Empty(returnedTodos);
        }

        [Fact]
        public async Task GetIncompleteTodosByCreationDate_WithValidDateRange_ReturnsFilteredTodos()
        {
            // Arrange
            var fromDate = DateTime.UtcNow.AddDays(-7);
            var toDate = DateTime.UtcNow;
            var incompleteTodos = new List<Todo>
            {
                new Todo { Id = "1", Name = "Todo 1", Completed = false, CreatedAt = DateTime.UtcNow.AddDays(-5) },
                new Todo { Id = "2", Name = "Todo 2", Completed = false, CreatedAt = DateTime.UtcNow.AddDays(-3) }
            };

            _mockTodoService.Setup(s => s.GetIncompleteTodosByCreationDateAsync(fromDate, toDate))
                           .ReturnsAsync(incompleteTodos);

            // Act
            var result = await _controller.GetIncompleteTodosByCreationDate(fromDate, toDate);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedTodos = Assert.IsType<List<Todo>>(okResult.Value);
            Assert.Equal(2, returnedTodos.Count);
            Assert.All(returnedTodos, todo => Assert.False(todo.Completed));
        }

        [Fact]
        public async Task GetIncompleteTodosByCreationDate_WithoutToDate_ReturnsFilteredTodos()
        {
            // Arrange
            var fromDate = DateTime.UtcNow.AddDays(-7);
            var incompleteTodos = new List<Todo>
            {
                new Todo { Id = "1", Name = "Todo 1", Completed = false, CreatedAt = DateTime.UtcNow.AddDays(-5) },
                new Todo { Id = "2", Name = "Todo 2", Completed = false, CreatedAt = DateTime.UtcNow.AddDays(-1) }
            };

            _mockTodoService.Setup(s => s.GetIncompleteTodosByCreationDateAsync(fromDate, null))
                           .ReturnsAsync(incompleteTodos);

            // Act
            var result = await _controller.GetIncompleteTodosByCreationDate(fromDate, null);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedTodos = Assert.IsType<List<Todo>>(okResult.Value);
            Assert.Equal(2, returnedTodos.Count);
            Assert.All(returnedTodos, todo => Assert.False(todo.Completed));
        }
    }
}