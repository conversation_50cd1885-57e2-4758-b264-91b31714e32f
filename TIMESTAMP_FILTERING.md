# Todo Timestamp Filtering Feature

## Overview

The Todo API now includes timestamp functionality that allows you to filter incomplete todos by their creation date. Each todo now automatically tracks when it was created and last updated.

## New Fields

The `Todo` model now includes two new timestamp fields:

- `CreatedAt`: Automatically set when a todo is created (UTC)
- `UpdatedAt`: Automatically updated whenever a todo is modified (UTC)

## API Endpoints

### Get Incomplete Todos by Creation Date

**Endpoint:** `GET /api/todos/incomplete`

**Query Parameters:**
- `fromDate` (required): Start date for filtering (ISO 8601 format)
- `toDate` (optional): End date for filtering (ISO 8601 format)

**Description:** Returns all incomplete todos that were created within the specified date range.

### Examples

#### Get incomplete todos created in the last 7 days:
```
GET /api/todos/incomplete?fromDate=2024-01-20T00:00:00Z
```

#### Get incomplete todos created between specific dates:
```
GET /api/todos/incomplete?fromDate=2024-01-20T00:00:00Z&toDate=2024-01-27T23:59:59Z
```

#### Response Format:
```json
[
  {
    "id": "507f1f77bcf86cd799439011",
    "name": "Complete project documentation",
    "completed": false,
    "createdAt": "2024-01-22T10:30:00Z",
    "updatedAt": "2024-01-22T10:30:00Z"
  },
  {
    "id": "507f1f77bcf86cd799439012",
    "name": "Review pull requests",
    "completed": false,
    "createdAt": "2024-01-23T14:15:00Z",
    "updatedAt": "2024-01-24T09:20:00Z"
  }
]
```

## Automatic Timestamp Management

- **Creation**: When a new todo is created, both `CreatedAt` and `UpdatedAt` are set to the current UTC time
- **Updates**: When a todo is updated, only `UpdatedAt` is modified to the current UTC time
- **CreatedAt** remains unchanged throughout the todo's lifecycle

## Use Cases

This feature is particularly useful for:

1. **Task Management**: Find todos that have been pending for too long
2. **Reporting**: Generate reports on todo creation patterns
3. **Cleanup**: Identify old incomplete todos that might need attention
4. **Analytics**: Track productivity patterns over time

## Implementation Details

- All timestamps are stored in UTC format
- The filtering is performed at the database level for optimal performance
- Results are sorted by creation date (oldest first)
- Only incomplete todos (completed = false) are returned

## Testing

The feature includes comprehensive unit tests covering:
- Timestamp setting during creation and updates
- Date range filtering functionality
- Controller endpoint behavior
- Edge cases and validation

Run tests with:
```bash
dotnet test TodoAPI.Tests/TodoAPI.Tests.csproj
```
