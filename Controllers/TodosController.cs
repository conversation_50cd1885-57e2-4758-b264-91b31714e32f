using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using TodoAPI.Models;
using TodoAPI.Services;

namespace TodoAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TodosController(TodoService todoService) : ControllerBase
    {
        private readonly TodoService _todoService = todoService;

        [HttpGet]
        public async Task<ActionResult<List<Todo>>> GetTodos()
        {
            var todos = await _todoService.GetTodosAsync();
            return Ok(todos);
        }

        [HttpGet("{id:length(24)}", Name = "GetTodo")]
        public async Task<ActionResult<Todo>> GetTodo(string id)
        {
            var todo = await _todoService.GetTodoAsync(id);
            if (todo == null)
            {
                return NotFound();
            }
            return Ok(todo);
        }

        [HttpPost]
        [Authorize]
        public async Task<ActionResult<Todo>> CreateTodo([FromBody] Todo todo)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var createdTodo = await _todoService.CreateTodoAsync(todo);
            return CreatedAtRoute("GetTodo", new { id = createdTodo.Id?.ToString() }, createdTodo);
        }

        [HttpPut("{id:length(24)}")]
        public async Task<IActionResult> UpdateTodo(string id, [FromBody] Todo newTodo)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var todo = await _todoService.GetTodoAsync(id);
            if (todo == null)
            {
                return NotFound();
            }
            await _todoService.UpdateTodoAsync(id, newTodo);
            return NoContent();
        }

        [HttpDelete("{id:length(24)}")]
        public async Task<IActionResult> DeleteTodo(string id)
        {
            var todo = await _todoService.GetTodoAsync(id);
            if (todo == null)
            {
                return NotFound();
            }
            await _todoService.DeleteTodoAsync(id);
            return NoContent();
        }

        [HttpGet("search/{name}")]
        public async Task<ActionResult<List<Todo>>> SearchTodosByName(string name)
        {
            var todos = await _todoService.SearchTodosByNameAsync(name);
            return Ok(todos);
        }

        [HttpGet("incomplete")]
        public async Task<ActionResult<List<Todo>>> GetIncompleteTodosByCreationDate(
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime? toDate = null)
        {
            var todos = await _todoService.GetIncompleteTodosByCreationDateAsync(fromDate, toDate);
            return Ok(todos);
        }

        [HttpGet("search/tag/{tag}")]
        public async Task<ActionResult<List<Todo>>> SearchTodosByTag(string tag)
        {
            var todos = await _todoService.SearchTodosByTagAsync(tag);
            return Ok(todos);
        }

        [HttpGet("search/tags")]
        public async Task<ActionResult<List<Todo>>> SearchTodosByTags([FromQuery] List<string> tags)
        {
            if (tags == null || tags.Count == 0)
            {
                return BadRequest("At least one tag must be provided");
            }
            var todos = await _todoService.SearchTodosByTagsAsync(tags);
            return Ok(todos);
        }
    }
}